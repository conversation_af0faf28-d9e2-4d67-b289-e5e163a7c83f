import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';

/// شاشة تحرير كشف راتب مكتملة
class EditPayrollScreen extends StatefulWidget {
  const EditPayrollScreen({super.key});

  @override
  State<EditPayrollScreen> createState() => _EditPayrollScreenState();
}

class _EditPayrollScreenState extends State<EditPayrollScreen> {
  final _formKey = GlobalKey<FormState>();
  late PayrollController _payrollController;

  // Controllers للحقول
  final _employeeIdController = TextEditingController();
  final _employeeNameController = TextEditingController();
  final _basicSalaryController = TextEditingController();
  final _allowancesController = TextEditingController();
  final _bonusesController = TextEditingController();
  final _incentivesController = TextEditingController();
  final _deductionsController = TextEditingController();
  final _advanceDeductionsController = TextEditingController();
  final _financialAssistanceController = TextEditingController();
  final _notesController = TextEditingController();

  // متغيرات الحالة
  int? _payrollId;
  EmployeePayroll? _currentPayroll;
  DateTime? _payPeriodStart;
  DateTime? _payPeriodEnd;
  double _netSalary = 0.0;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadPayrollData();
  }

  void _initializeControllers() {
    _payrollController = Get.find<PayrollController>();

    // إضافة مستمعين لحساب الراتب الصافي
    _basicSalaryController.addListener(_calculateNetSalary);
    _allowancesController.addListener(_calculateNetSalary);
    _bonusesController.addListener(_calculateNetSalary);
    _incentivesController.addListener(_calculateNetSalary);
    _deductionsController.addListener(_calculateNetSalary);
    _advanceDeductionsController.addListener(_calculateNetSalary);
    _financialAssistanceController.addListener(_calculateNetSalary);
  }

  Future<void> _loadPayrollData() async {
    try {
      final String? idParam = Get.parameters['id'];
      if (idParam == null) {
        setState(() {
          _isLoading = false;
        });
        Get.snackbar('خطأ', 'معرف كشف الراتب غير صحيح');
        return;
      }

      _payrollId = int.tryParse(idParam);
      if (_payrollId == null) {
        setState(() {
          _isLoading = false;
        });
        Get.snackbar('خطأ', 'معرف كشف الراتب غير صحيح');
        return;
      }

      // تحميل بيانات كشف الراتب
      _currentPayroll = await _payrollController.getPayrollById(_payrollId!);

      if (_currentPayroll != null) {
        _populateForm();
      }

      setState(() {
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      Get.snackbar('خطأ', 'فشل في تحميل بيانات كشف الراتب: $e');
    }
  }

  void _populateForm() {
    if (_currentPayroll == null) return;

    final payroll = _currentPayroll!;

    _employeeIdController.text = payroll.employeeId.toString();
    _employeeNameController.text = payroll.employeeNameArabic ?? payroll.employeeName ?? '';
    _basicSalaryController.text = payroll.basicSalary.toString();
    _allowancesController.text = payroll.allowances.toString();
    _bonusesController.text = payroll.bonuses.toString();
    _incentivesController.text = payroll.incentives.toString();
    _deductionsController.text = payroll.deductions.toString();
    _advanceDeductionsController.text = payroll.advanceDeductions.toString();
    _financialAssistanceController.text = payroll.financialAssistance.toString();
    _notesController.text = payroll.notes ?? '';

    _payPeriodStart = payroll.payPeriodStart;
    _payPeriodEnd = payroll.payPeriodEnd;

    _calculateNetSalary();
  }

  void _calculateNetSalary() {
    final basicSalary = double.tryParse(_basicSalaryController.text) ?? 0.0;
    final allowances = double.tryParse(_allowancesController.text) ?? 0.0;
    final bonuses = double.tryParse(_bonusesController.text) ?? 0.0;
    final incentives = double.tryParse(_incentivesController.text) ?? 0.0;
    final financialAssistance = double.tryParse(_financialAssistanceController.text) ?? 0.0;
    final deductions = double.tryParse(_deductionsController.text) ?? 0.0;
    final advanceDeductions = double.tryParse(_advanceDeductionsController.text) ?? 0.0;

    final totalEarnings = basicSalary + allowances + bonuses + incentives + financialAssistance;
    final totalDeductions = deductions + advanceDeductions;

    setState(() {
      _netSalary = totalEarnings - totalDeductions;
    });
  }

  Future<void> _updatePayroll() async {
    if (!_formKey.currentState!.validate()) {
      Get.snackbar('خطأ في البيانات', 'يرجى التحقق من البيانات المدخلة');
      return;
    }

    if (_currentPayroll?.paymentStatus == 'paid') {
      Get.snackbar('خطأ', 'لا يمكن تعديل راتب مدفوع');
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final updateDto = UpdatePayrollDto(
        basicSalary: double.parse(_basicSalaryController.text),
        allowances: double.parse(_allowancesController.text),
        bonuses: double.parse(_bonusesController.text),
        incentives: double.parse(_incentivesController.text),
        deductions: double.parse(_deductionsController.text),
        advanceDeductions: double.parse(_advanceDeductionsController.text),
        financialAssistance: double.parse(_financialAssistanceController.text),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        payPeriodStart: _payPeriodStart!,
        payPeriodEnd: _payPeriodEnd!,
      );

      final success = await _payrollController.updatePayroll(_payrollId!, updateDto);

      if (success) {
        Get.snackbar('تم التحديث', 'تم تحديث كشف الراتب بنجاح');
        Get.back(result: true);
      }
    } catch (e) {
      Get.snackbar('خطأ في التحديث', 'حدث خطأ أثناء تحديث البيانات: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('تحرير كشف راتب'),
          backgroundColor: Colors.purple.shade700,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تحميل بيانات كشف الراتب...'),
            ],
          ),
        ),
      );
    }

    if (_currentPayroll == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('تحرير كشف راتب'),
          backgroundColor: Colors.purple.shade700,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('لم يتم العثور على كشف الراتب'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تحرير كشف راتب'),
        backgroundColor: Colors.purple.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            onPressed: _isSaving ? null : _updatePayroll,
            tooltip: _isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // تحذير إذا كان الراتب مدفوع
              if (_currentPayroll?.paymentStatus == 'paid')
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'تحذير: هذا الراتب مدفوع ولا يمكن تعديله',
                          style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),

              _buildEmployeeInfoCard(),
              const SizedBox(height: 16),
              _buildSalaryComponentsCard(),
              const SizedBox(height: 16),
              _buildNetSalaryCard(),
              const SizedBox(height: 16),
              _buildNotesCard(),
              const SizedBox(height: 24),
              _buildUpdateButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الموظف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _employeeIdController,
                    decoration: const InputDecoration(
                      labelText: 'معرف الموظف',
                      prefixIcon: Icon(Icons.badge),
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _employeeNameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم الموظف',
                      prefixIcon: Icon(Icons.person),
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'فترة الراتب',
                      prefixIcon: Icon(Icons.date_range),
                      border: OutlineInputBorder(),
                    ),
                    child: Text(
                      _payPeriodStart != null && _payPeriodEnd != null
                          ? '${_payPeriodStart!.day}/${_payPeriodStart!.month}/${_payPeriodStart!.year} - ${_payPeriodEnd!.day}/${_payPeriodEnd!.month}/${_payPeriodEnd!.year}'
                          : 'غير محدد',
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'حالة الدفع',
                      prefixIcon: Icon(Icons.payment),
                      border: OutlineInputBorder(),
                    ),
                    child: Text(
                      _getPaymentStatusText(_currentPayroll?.paymentStatus ?? ''),
                      style: TextStyle(
                        color: _getPaymentStatusColor(_currentPayroll?.paymentStatus ?? ''),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryComponentsCard() {
    final isReadOnly = _currentPayroll?.paymentStatus == 'paid';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مكونات الراتب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // المكاسب
            const Text(
              'المكاسب',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.green),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _basicSalaryController,
                    decoration: const InputDecoration(
                      labelText: 'الراتب الأساسي',
                      prefixIcon: Icon(Icons.attach_money),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    readOnly: isReadOnly,
                    style: TextStyle(color: isReadOnly ? Colors.grey[600] : null),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _allowancesController,
                    decoration: const InputDecoration(
                      labelText: 'البدلات',
                      prefixIcon: Icon(Icons.add_circle),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    readOnly: isReadOnly,
                    style: TextStyle(color: isReadOnly ? Colors.grey[600] : null),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _bonusesController,
                    decoration: const InputDecoration(
                      labelText: 'المكافآت',
                      prefixIcon: Icon(Icons.star),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    readOnly: isReadOnly,
                    style: TextStyle(color: isReadOnly ? Colors.grey[600] : null),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _incentivesController,
                    decoration: const InputDecoration(
                      labelText: 'الحوافز',
                      prefixIcon: Icon(Icons.trending_up),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    readOnly: isReadOnly,
                    style: TextStyle(color: isReadOnly ? Colors.grey[600] : null),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetSalaryCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'الراتب الصافي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '${_netSalary.toStringAsFixed(2)} ريال',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: _netSalary >= 0 ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    final isReadOnly = _currentPayroll?.paymentStatus == 'paid';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
                hintText: 'أي ملاحظات حول كشف الراتب',
              ),
              maxLines: 3,
              readOnly: isReadOnly,
              style: TextStyle(color: isReadOnly ? Colors.grey[600] : null),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateButton() {
    if (_currentPayroll?.paymentStatus == 'paid') {
      return const SizedBox.shrink();
    }

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _updatePayroll,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple.shade700,
          foregroundColor: Colors.white,
        ),
        child: _isSaving
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('جاري الحفظ...'),
                ],
              )
            : const Text(
                'حفظ التغييرات',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  String _getPaymentStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلق';
      case 'paid':
        return 'مدفوع';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  Color _getPaymentStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _employeeIdController.dispose();
    _employeeNameController.dispose();
    _basicSalaryController.dispose();
    _allowancesController.dispose();
    _bonusesController.dispose();
    _incentivesController.dispose();
    _deductionsController.dispose();
    _advanceDeductionsController.dispose();
    _financialAssistanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
