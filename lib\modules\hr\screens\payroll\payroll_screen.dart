import 'package:flutter/material.dart';
import 'package:flutter_application_2/modules/hr/screens/hr_screens.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../routes/hr_routes.dart';

/// شاشة إدارة الرواتب
class PayrollScreen extends StatelessWidget {
  const PayrollScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // التأكد من تسجيل Controllers
    if (!Get.isRegistered<PayrollController>()) {
      Get.lazyPut(() => PayrollController());
    }
    
    final payrollController = Get.find<PayrollController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الرواتب'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => Get.to(AddPayrollScreen()),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => payrollController.loadPayrolls(refresh: true),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          _buildSearchAndFilterBar(payrollController),
          
          // إحصائيات سريعة
          _buildQuickStats(payrollController),
          
          // قائمة الرواتب
          Expanded(
            child: _buildPayrollList(payrollController),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => HRNavigator.toAddPayroll(),
        tooltip: 'إضافة كشف راتب جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilterBar(PayrollController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في الرواتب...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              controller.searchTerm.value = value;
              controller.loadPayrolls(refresh: true);
            },
          ),
          const SizedBox(height: 12),
          
          // فلاتر
          Row(
            children: [
              Expanded(
                child: Obx(() => DropdownButtonFormField<String>(
                  value: controller.selectedStatus.value.isEmpty 
                      ? null 
                      : controller.selectedStatus.value,
                  decoration: const InputDecoration(
                    labelText: 'حالة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'pending', child: Text('معلق')),
                    DropdownMenuItem(value: 'paid', child: Text('مدفوع')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                  ],
                  onChanged: (value) {
                    controller.selectedStatus.value = value ?? '';
                    controller.loadPayrolls(refresh: true);
                  },
                )),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Obx(() => DropdownButtonFormField<int>(
                  value: controller.selectedMonth.value == 0 
                      ? null 
                      : controller.selectedMonth.value,
                  decoration: const InputDecoration(
                    labelText: 'الشهر',
                    border: OutlineInputBorder(),
                  ),
                  items: List.generate(12, (index) => DropdownMenuItem(
                    value: index + 1,
                    child: Text(_getMonthName(index + 1)),
                  )),
                  onChanged: (value) {
                    controller.selectedMonth.value = value ?? 0;
                    controller.loadPayrolls(refresh: true);
                  },
                )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(PayrollController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() {
        final stats = controller.statistics.value;
        if (stats == null) return const SizedBox.shrink();
        
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الرواتب',
                stats.totalPayrolls.toString(),
                Icons.receipt_long,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'المدفوع',
                stats.paidPayrolls.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'المعلق',
                stats.pendingPayrolls.toString(),
                Icons.pending,
                Colors.orange,
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPayrollList(PayrollController controller) {
    return Obx(() {
      if (controller.isLoading.value && controller.payrolls.isEmpty) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.error.value.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                controller.error.value,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.loadPayrolls(refresh: true),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      if (controller.payrolls.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.payment, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد رواتب',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              SizedBox(height: 8),
              Text(
                'اضغط على + لإضافة كشف راتب جديد',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () => controller.loadPayrolls(refresh: true),
        child: ListView.builder(
          itemCount: controller.payrolls.length + 
                   (controller.hasMoreData.value ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == controller.payrolls.length) {
              // Loading indicator for pagination
              if (controller.isLoadingMore.value) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                );
              } else {
                // Load more button
                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: () => controller.loadMorePayrolls(),
                    child: const Text('تحميل المزيد'),
                  ),
                );
              }
            }

            final payroll = controller.payrolls[index];
            return _buildPayrollCard(payroll);
          },
        ),
      );
    });
  }

  Widget _buildPayrollCard(EmployeePayroll payroll) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(payroll.paymentStatus),
          child: Icon(
            _getStatusIcon(payroll.paymentStatus),
            color: Colors.white,
          ),
        ),
        title: Text(
          payroll.employeeNameArabic ?? payroll.employeeName ?? 'غير محدد',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الفترة: ${_formatPeriod(payroll.payPeriodStart, payroll.payPeriodEnd)}'),
            Text('الراتب الصافي: ${payroll.netSalary.toStringAsFixed(2)} ريال'),
            Text('الحالة: ${_getStatusText(payroll.paymentStatus)}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handlePayrollAction(value, payroll),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: ListTile(
                leading: Icon(Icons.visibility),
                title: Text('عرض التفاصيل'),
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
              ),
            ),
            if (payroll.paymentStatus == 'pending')
              const PopupMenuItem(
                value: 'approve',
                child: ListTile(
                  leading: Icon(Icons.check),
                  title: Text('اعتماد الدفع'),
                ),
              ),
          ],
        ),
        onTap: () => _viewPayrollDetails(payroll),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'paid': return Colors.green;
      case 'pending': return Colors.orange;
      case 'cancelled': return Colors.red;
      default: return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'paid': return Icons.check_circle;
      case 'pending': return Icons.pending;
      case 'cancelled': return Icons.cancel;
      default: return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'cancelled': return 'ملغي';
      default: return 'غير محدد';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  }

  String _formatPeriod(DateTime start, DateTime end) {
    return '${start.day}/${start.month} - ${end.day}/${end.month}/${end.year}';
  }

  void _handlePayrollAction(String action, EmployeePayroll payroll) {
    switch (action) {
      case 'view':
        _viewPayrollDetails(payroll);
        break;
      case 'edit':
        HRNavigator.toEditPayroll(payroll.id);
        break;
      case 'approve':
        _approvePayment(payroll);
        break;
    }
  }

  void _viewPayrollDetails(EmployeePayroll payroll) {
    HRNavigator.toPayrollDetails(payroll.id);
  }

  void _approvePayment(EmployeePayroll payroll) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الدفع'),
        content: Text('هل تريد تأكيد دفع راتب ${payroll.employeeNameArabic}؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              final controller = Get.find<PayrollController>();
              controller.approvePayment(payroll.id);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}
