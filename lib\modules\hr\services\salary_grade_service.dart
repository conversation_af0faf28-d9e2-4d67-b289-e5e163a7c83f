/// خدمة حساب الراتب الأساسي بناءً على درجة الراتب
class SalaryGradeService {
  /// جدول درجات الرواتب والمبالغ المقابلة لها
  static const Map<String, double> _salaryGrades = {
    '1': 3000.00,
    '2': 3500.00,
    '3': 4000.00,
    '4': 4500.00,
    '5': 5000.00,
    '6': 5500.00,
    '7': 6000.00,
    '8': 6500.00,
    '9': 7000.00,
    '10': 7500.00,
    '11': 8000.00,
    '12': 8500.00,
    '13': 9000.00,
    '14': 9500.00,
    '15': 10000.00,
  };

  /// الحصول على الراتب الأساسي بناءً على درجة الراتب
  static double getBasicSalaryBySalaryGrade(String? salaryGrade) {
    if (salaryGrade == null || salaryGrade.isEmpty) {
      return 3000.00; // قيمة افتراضية
    }
    
    return _salaryGrades[salaryGrade] ?? 3000.00;
  }

  /// الحصول على جميع درجات الرواتب المتاحة
  static List<String> getAvailableSalaryGrades() {
    return _salaryGrades.keys.toList()..sort((a, b) => int.parse(a).compareTo(int.parse(b)));
  }

  /// الحصول على معلومات درجة الراتب مع المبلغ
  static Map<String, dynamic> getSalaryGradeInfo(String salaryGrade) {
    final basicSalary = getBasicSalaryBySalaryGrade(salaryGrade);
    return {
      'grade': salaryGrade,
      'basicSalary': basicSalary,
      'formattedSalary': formatCurrency(basicSalary),
      'description': 'الدرجة $salaryGrade - ${formatCurrency(basicSalary)}',
    };
  }

  /// تنسيق المبلغ كعملة
  static String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  /// التحقق من صحة درجة الراتب
  static bool isValidSalaryGrade(String? salaryGrade) {
    if (salaryGrade == null || salaryGrade.isEmpty) return false;
    return _salaryGrades.containsKey(salaryGrade);
  }

  /// الحصول على أقل راتب أساسي
  static double getMinBasicSalary() {
    return _salaryGrades.values.reduce((a, b) => a < b ? a : b);
  }

  /// الحصول على أعلى راتب أساسي
  static double getMaxBasicSalary() {
    return _salaryGrades.values.reduce((a, b) => a > b ? a : b);
  }

  /// الحصول على متوسط الراتب الأساسي
  static double getAverageBasicSalary() {
    final total = _salaryGrades.values.reduce((a, b) => a + b);
    return total / _salaryGrades.length;
  }

  /// البحث عن درجة الراتب بناءً على المبلغ
  static String? findSalaryGradeByAmount(double amount) {
    for (final entry in _salaryGrades.entries) {
      if (entry.value == amount) {
        return entry.key;
      }
    }
    return null;
  }

  /// الحصول على الدرجة التالية
  static String? getNextSalaryGrade(String currentGrade) {
    final currentGradeNum = int.tryParse(currentGrade);
    if (currentGradeNum == null) return null;
    
    final nextGrade = (currentGradeNum + 1).toString();
    return _salaryGrades.containsKey(nextGrade) ? nextGrade : null;
  }

  /// الحصول على الدرجة السابقة
  static String? getPreviousSalaryGrade(String currentGrade) {
    final currentGradeNum = int.tryParse(currentGrade);
    if (currentGradeNum == null || currentGradeNum <= 1) return null;
    
    final previousGrade = (currentGradeNum - 1).toString();
    return _salaryGrades.containsKey(previousGrade) ? previousGrade : null;
  }

  /// حساب الزيادة في الراتب عند الترقية
  static double calculateSalaryIncrease(String fromGrade, String toGrade) {
    final fromSalary = getBasicSalaryBySalaryGrade(fromGrade);
    final toSalary = getBasicSalaryBySalaryGrade(toGrade);
    return toSalary - fromSalary;
  }

  /// حساب نسبة الزيادة في الراتب
  static double calculateSalaryIncreasePercentage(String fromGrade, String toGrade) {
    final fromSalary = getBasicSalaryBySalaryGrade(fromGrade);
    final toSalary = getBasicSalaryBySalaryGrade(toGrade);
    
    if (fromSalary == 0) return 0;
    return ((toSalary - fromSalary) / fromSalary) * 100;
  }

  /// الحصول على قائمة بجميع درجات الرواتب مع التفاصيل
  static List<Map<String, dynamic>> getAllSalaryGradesWithDetails() {
    return _salaryGrades.entries.map((entry) {
      return {
        'grade': entry.key,
        'basicSalary': entry.value,
        'formattedSalary': formatCurrency(entry.value),
        'description': 'الدرجة ${entry.key}',
        'fullDescription': 'الدرجة ${entry.key} - ${formatCurrency(entry.value)}',
      };
    }).toList()..sort((a, b) => int.parse(a['grade']).compareTo(int.parse(b['grade'])));
  }
}
