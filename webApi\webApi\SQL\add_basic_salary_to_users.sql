-- إضافة حقل الراتب الأساسي إلى جدول المستخدمين
-- Add basic_salary field to users table

USE [TasksDB];
GO

PRINT 'بدء إضافة حقل الراتب الأساسي إلى جدول المستخدمين...';

-- التحقق من وجود العمود قبل الإضافة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'basic_salary')
BEGIN
    -- إضافة حقل الراتب الأساسي
    ALTER TABLE [dbo].[users] 
    ADD basic_salary DECIMAL(18,2) NULL;
    
    PRINT '✅ تم إضافة حقل basic_salary إلى جدول المستخدمين';
    
    -- تحديث الموظفين الحاليين بقيم افتراضية حسب الدرجة الوظيفية
    UPDATE users 
    SET basic_salary = 
        CASE 
            WHEN salary_grade = '1' THEN 3000.00
            WHEN salary_grade = '2' THEN 3500.00
            WHEN salary_grade = '3' THEN 4000.00
            WHEN salary_grade = '4' THEN 4500.00
            WHEN salary_grade = '5' THEN 5000.00
            WHEN salary_grade = '6' THEN 5500.00
            WHEN salary_grade = '7' THEN 6000.00
            WHEN salary_grade = '8' THEN 6500.00
            WHEN salary_grade = '9' THEN 7000.00
            WHEN salary_grade = '10' THEN 7500.00
            ELSE 3000.00 -- قيمة افتراضية
        END
    WHERE employee_id IS NOT NULL 
      AND employee_id != ''
      AND basic_salary IS NULL;
    
    PRINT '✅ تم تحديث الرواتب الأساسية للموظفين الحاليين';
    
    -- إضافة قيد للتأكد من أن الراتب الأساسي أكبر من صفر
    ALTER TABLE [dbo].[users]
    ADD CONSTRAINT CK_users_basic_salary_positive 
    CHECK (basic_salary IS NULL OR basic_salary >= 0);
    
    PRINT '✅ تم إضافة قيد التحقق من الراتب الأساسي';
END
ELSE
BEGIN
    PRINT '⚠️ حقل basic_salary موجود مسبقاً في جدول المستخدمين';
END

-- عرض إحصائيات الرواتب
SELECT 
    COUNT(*) as total_employees,
    COUNT(basic_salary) as employees_with_salary,
    AVG(basic_salary) as average_salary,
    MIN(basic_salary) as min_salary,
    MAX(basic_salary) as max_salary
FROM users 
WHERE employee_id IS NOT NULL AND employee_id != '';

PRINT 'تم الانتهاء من إضافة حقل الراتب الأساسي';
