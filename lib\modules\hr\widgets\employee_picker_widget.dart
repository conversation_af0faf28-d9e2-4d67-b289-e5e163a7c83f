import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/hr_controllers.dart';
import '../models/hr_models.dart';

/// Widget لاختيار الموظف مع البحث والتصفية
class EmployeePickerWidget extends StatefulWidget {
  final Employee? selectedEmployee;
  final Function(Employee?) onEmployeeSelected;
  final String? labelText;
  final String? hintText;
  final bool isRequired;
  final String? Function(Employee?)? validator;

  const EmployeePickerWidget({
    super.key,
    this.selectedEmployee,
    required this.onEmployeeSelected,
    this.labelText = 'الموظف',
    this.hintText = 'اختر الموظف',
    this.isRequired = true,
    this.validator,
  });

  @override
  State<EmployeePickerWidget> createState() => _EmployeePickerWidgetState();
}

class _EmployeePickerWidgetState extends State<EmployeePickerWidget> {
  late EmployeeController _employeeController;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isDropdownOpen = false;

  @override
  void initState() {
    super.initState();
    _initializeController();
    _loadEmployees();
  }

  void _initializeController() {
    if (!Get.isRegistered<EmployeeController>()) {
      Get.lazyPut(() => EmployeeController());
    }
    _employeeController = Get.find<EmployeeController>();
  }

  Future<void> _loadEmployees() async {
    await _employeeController.loadEmployees(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.labelText!,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),
        _buildEmployeeSelector(),
        if (widget.validator != null && widget.selectedEmployee == null && widget.isRequired)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              widget.validator!(null) ?? '',
              style: TextStyle(color: Theme.of(context).colorScheme.error, fontSize: 12),
            ),
          ),
      ],
    );
  }

  Widget _buildEmployeeSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // حقل البحث والعرض
          InkWell(
            onTap: () => _showEmployeeSelectionDialog(),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Icon(Icons.person, color: Colors.grey),
                  const SizedBox(width: 12),
                  Expanded(
                    child: widget.selectedEmployee != null
                        ? _buildSelectedEmployeeDisplay()
                        : Text(
                            widget.hintText ?? 'اختر الموظف',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                          ),
                  ),
                  const Icon(Icons.arrow_drop_down, color: Colors.grey),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedEmployeeDisplay() {
    final employee = widget.selectedEmployee!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          employee.displayName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (employee.employeeId?.isNotEmpty == true)
          Text(
            'رقم الموظف: ${employee.employeeId}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        if (employee.departmentName?.isNotEmpty == true)
          Text(
            'القسم: ${employee.departmentName}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
      ],
    );
  }

  void _showEmployeeSelectionDialog() {
    _searchController.clear();
    _employeeController.searchTerm.value = '';
    _loadEmployees();

    Get.dialog(
      Dialog(
        child: Container(
          width: Get.width * 0.9,
          height: Get.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // عنوان الحوار
              Row(
                children: [
                  const Icon(Icons.person_search, size: 24),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'اختيار الموظف',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              
              // حقل البحث
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'البحث في الموظفين',
                    hintText: 'ادخل اسم الموظف أو رقم الموظف أو المنصب',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _employeeController.searchEmployees(value);
                  },
                ),
              ),
              
              // قائمة الموظفين
              Expanded(
                child: Obx(() {
                  if (_employeeController.isLoading.value) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (_employeeController.employees.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.person_off, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('لا توجد موظفين'),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: _employeeController.employees.length,
                    itemBuilder: (context, index) {
                      final employee = _employeeController.employees[index];
                      return _buildEmployeeListItem(employee);
                    },
                  );
                }),
              ),
              
              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 8),
                  if (widget.selectedEmployee != null)
                    TextButton(
                      onPressed: () {
                        widget.onEmployeeSelected(null);
                        Get.back();
                      },
                      child: const Text('مسح الاختيار'),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeListItem(Employee employee) {
    final isSelected = widget.selectedEmployee?.id == employee.id;
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      color: isSelected ? Colors.blue.shade50 : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isSelected ? Colors.blue : Colors.grey.shade300,
          child: Text(
            employee.displayName.isNotEmpty 
                ? employee.displayName[0].toUpperCase()
                : 'م',
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.grey.shade700,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          employee.displayName,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (employee.employeeId?.isNotEmpty == true)
              Text('رقم الموظف: ${employee.employeeId}'),
            if (employee.jobTitle?.isNotEmpty == true)
              Text('المنصب: ${employee.jobTitle}'),
            if (employee.departmentName?.isNotEmpty == true)
              Text('القسم: ${employee.departmentName}'),
          ],
        ),
        trailing: isSelected 
            ? const Icon(Icons.check_circle, color: Colors.blue)
            : null,
        onTap: () {
          widget.onEmployeeSelected(employee);
          Get.back();
        },
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
